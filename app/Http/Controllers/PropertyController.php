<?php

namespace App\Http\Controllers;

use App\Models\Property;
use App\Models\User; // Added for Lokus MVP
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use App\Http\Middleware\RoleMiddleware; // Import the middleware
use Intervention\Image\ImageManager; // Import the ImageManager
use Intervention\Image\Drivers\Gd\Driver; // Import the GD driver

class PropertyController extends Controller
{
    /**
     * Display a listing of the properties.
     */
    public function index()
    {
        return view('properties.index');
    }

    /**
     * Display the specified property.
     */
    public function show(Property $property)
    {
        if ($property->status !== 'published' && (!Auth::check() || Auth::id() !== $property->user_id)) {
            abort(404); // Only published properties are publicly viewable, or owner can view drafts
        }
        return view('properties.show', compact('property'));
    }

    /**
     * Show the form for creating a new property.
     */
    public function create()
    {
        return view('properties.create');
    }


    /**
     * Display a listing of the authenticated user's properties.
     */
    public function myProperties()
    {
        return view('properties.my-properties');
    }

    /**
     * Show the form for editing the specified property.
     */
    public function edit(Property $property)
    {
        if (Auth::id() !== $property->user_id) {
            abort(403);
        }
        return view('properties.edit', compact('property'));
    }

    /**
     * Update the specified property in storage.
     */
    public function update(Request $request, Property $property)
    {
        if (Auth::id() !== $property->user_id) {
            abort(403);
        }

        $request->validate([
            'property_type' => 'required|string|max:255',
            'listing_type' => 'required|string|in:For Sale,For Rent',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'currency' => 'nullable|string|max:10',
            'address_line_1' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'state_region' => 'nullable|string|max:255',
            'zip_code' => 'nullable|string|max:20',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'features' => 'nullable|string', // Validate as string first
            'status' => 'nullable|string|in:draft,published,sold,rented,under_offer',
        ]);

        // Parse features JSON string to array
        $features = json_decode($request->input('features', '{}'), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return back()->withErrors(['features' => 'The features field must be a valid JSON string.'])->withInput();
        }

        $property->fill($request->except(['images', 'features']));
        $property->currency = $request->input('currency', 'USD');

        // Handle image uploads (add new, keep existing if not replaced)
        $imagePaths = $property->images ?? [];
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $filename = uniqid() . '.' . $image->getClientOriginalExtension();
                $path = 'public/properties/' . $filename;

                // Use Intervention Image to resize and save
                $manager = new ImageManager(new Driver());
                $manager->read($image)->resize(800, null, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                })->save(storage_path('app/' . $path));

                $imagePaths[] = str_replace('public/', '', $path); // Store relative path only
            }
        }
        $property->images = $imagePaths;

        // Assign parsed features
        $property->features = $features;

        $property->save();

        return redirect()->route('properties.my')->with('success', 'Property updated successfully!');
    }

    /**
     * Remove the specified property from storage.
     */
    public function destroy(Property $property)
    {
        if (Auth::id() !== $property->user_id) {
            abort(403);
        }

        // Delete associated images from storage
        if ($property->images) {
            foreach ($property->images as $imagePath) {
                Storage::delete(str_replace('/storage/', 'public/', $imagePath));
            }
        }

        $property->delete();

        return redirect()->route('properties.my')->with('success', 'Property deleted successfully!');
    }

    /**
     * Update the status of the specified property.
     */
    public function updateStatus(Request $request, Property $property)
    {
        if (Auth::id() !== $property->user_id) {
            abort(403);
        }

        $request->validate([
            'status' => 'required|string|in:draft,published,sold,rented,under_offer',
        ]);

        $property->status = $request->input('status');
        $property->save();

        return redirect()->route('lister.properties.index')->with('success', 'Property status updated successfully!');
    }
}
