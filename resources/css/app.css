@import "tailwindcss";
@source "../views";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import admin dashboard styles */
@import "./admin-dashboard.css";

[x-cloak] { display: none !important; }

/* General Styles */
.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Layout Improvements */
.sidebar-layout {
    @apply flex h-screen bg-gray-50;
}

.sidebar-content {
    @apply flex-1 flex flex-col overflow-hidden;
}

.main-content {
    @apply flex-1 overflow-y-auto;
}

/* Component Consistency */
.page-header {
    @apply mb-8;
}

.page-title {
    @apply text-3xl font-bold text-gray-900;
}

.page-subtitle {
    @apply mt-2 text-gray-600;
}

/* Authentication Pages */
.auth-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.auth-card {
    @apply rounded-2xl border border-white/20 bg-white/80 backdrop-blur-sm shadow-xl;
}

.auth-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors;
}

.auth-button {
    @apply w-full h-12 px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

.auth-link {
    @apply text-blue-600 hover:text-blue-700 transition-colors;
}

/* Role Selection Cards */
.role-card {
    @apply relative flex cursor-pointer rounded-lg border p-4 focus:outline-none transition-all;
}

.role-card-selected {
    @apply border-blue-600 bg-blue-50 ring-2 ring-blue-600;
}

.role-card-unselected {
    @apply border-gray-300 bg-white hover:bg-gray-50;
}

/* Demo Credentials Box */
.demo-credentials {
    @apply mt-8 p-4 bg-gray-50 rounded-lg border border-gray-200;
}
