<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Lokus') }}</title>

        <!-- Fonts -->
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

        <!-- Scripts -->
        @livewireStyles
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
        @vite(['resources/css/app.css', 'resources/js/app.js'])

    </head>
    <body class="bg-gray-50" x-data="{ 
        currentPage: '{{ request()->path() }}', 
        showMobileMenu: false,
        showLoginModal: false,
        showContactModal: false,
        
        isActive(path) {
            return this.currentPage === path || this.currentPage.startsWith(path + '/');
        }
    }">

        <!-- Navigation -->
        <nav class="bg-white shadow-lg sticky top-0 z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 flex items-center cursor-pointer" @click="currentPage = 'home'">
                            <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                Lokus
                            </h1>
                        </div>
                    </div>
                    
                    <div class="hidden md:flex items-center space-x-6">
                        <a href="{{ url('/') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors {{ request()->is('/') ? 'text-blue-600' : '' }}">Home</a>
                        <a href="{{ route('properties.index') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors {{ request()->routeIs('properties.index') ? 'text-blue-600' : '' }}">Search</a>
                        @auth
                            <a href="{{ route('lister.properties.index') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors {{ request()->routeIs('lister.properties.*') ? 'text-blue-600' : '' }}">My Listings</a>
                            <a href="{{ route('lister.properties.create') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors {{ request()->routeIs('lister.properties.create') ? 'text-blue-600' : '' }}">Create Listing</a>
                            <form method="POST" action="{{ route('logout') }}" class="inline">
                                @csrf
                                <a href="{{ route('logout') }}"
                                   onclick="event.preventDefault(); this.closest('form').submit();"
                                   class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
                                    Logout
                                </a>
                            </form>
                        @else
                            <button @click="showLoginModal = true" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                Sign In
                            </button>
                            @if (Route::has('register'))
                                <a href="{{ route('register') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">Register</a>
                            @endif
                        @endauth
                    </div>
                    
                    <div class="md:hidden flex items-center">
                        <button @click="showMobileMenu = !showMobileMenu" class="text-gray-700">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Mobile menu -->
            <div x-show="showMobileMenu" x-cloak class="md:hidden bg-white border-t">
                <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                    <a href="{{ url('/') }}" @click="showMobileMenu = false" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 {{ request()->is('/') ? 'text-blue-600' : '' }}">Home</a>
                    <a href="{{ route('properties.index') }}" @click="showMobileMenu = false" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 {{ request()->routeIs('properties.index') ? 'text-blue-600' : '' }}">Search</a>
                    @auth
                        <a href="{{ route('lister.properties.index') }}" @click="showMobileMenu = false" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 {{ request()->routeIs('lister.properties.*') ? 'text-blue-600' : '' }}">My Listings</a>
                        <a href="{{ route('lister.properties.create') }}" @click="showMobileMenu = false" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 {{ request()->routeIs('lister.properties.create') ? 'text-blue-600' : '' }}">Create Listing</a>
                        <form method="POST" action="{{ route('logout') }}" class="block">
                            @csrf
                            <a href="{{ route('logout') }}"
                               onclick="event.preventDefault(); this.closest('form').submit(); showMobileMenu = false;"
                               class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600">
                                Logout
                            </a>
                        </form>
                    @else
                        <button @click="showLoginModal = true; showMobileMenu = false" class="block w-full text-left px-3 py-2 text-base font-medium text-blue-600">Sign In</button>
                        @if (Route::has('register'))
                             <a href="{{ route('register') }}" @click="showMobileMenu = false" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600">Register</a>
                        @endif
                    @endauth
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <main class="mt-4">
            {{ $slot }}
        </main>

        <!-- Login Modal -->
        <div x-show="showLoginModal" x-cloak class="fixed inset-0 z-50 overflow-y-auto">
            <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showLoginModal = false"></div>
                
                <div class="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white px-8 pt-8 pb-6">
                        <div class="text-center mb-8">
                            <h3 class="text-2xl font-bold text-gray-900">Welcome Back</h3>
                            <p class="text-gray-600">Sign in to your account</p>
                        </div>
                        
                        <form method="POST" action="{{ route('login') }}" class="space-y-6">
                            @csrf
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                <input id="email" name="email" type="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="<EMAIL>" required>
                            </div>
                            <div>
                                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                                <input id="password" name="password" type="password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="••••••••" required>
                            </div>
                            <div class="flex items-center justify-between">
                                <label class="flex items-center">
                                    <input type="checkbox" name="remember" class="text-blue-600 mr-2">
                                    <span class="text-sm text-gray-600">Remember me</span>
                                </label>
                                <a href="{{ route('password.request') }}" class="text-sm text-blue-600 hover:text-blue-700">Forgot password?</a>
                            </div>
                            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors">
                                Sign In
                            </button>
                        </form>
                        
                        <div class="mt-6 text-center">
                            <p class="text-sm text-gray-600">
                                Don't have an account? 
                                <a href="{{ route('register') }}" class="text-blue-600 hover:text-blue-700 font-medium">Sign up</a>
                            </p>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 px-8 py-4">
                        <button @click="showLoginModal = false" class="w-full text-gray-600 hover:text-gray-700 font-medium">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Modal -->
        <div x-show="showContactModal" x-cloak class="fixed inset-0 z-50 overflow-y-auto">
            <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showContactModal = false"></div>
                
                <div class="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white px-8 pt-8 pb-6">
                        <div class="text-center mb-8">
                            <h3 class="text-2xl font-bold text-gray-900">Contact Lister</h3>
                            <p class="text-gray-600">Send a message about this property</p>
                        </div>
                        
                        <form class="space-y-6">
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                    <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="John">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                    <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Doe">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                <input type="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="<EMAIL>">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Phone (Optional)</label>
                                <input type="tel" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="+****************">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                                <textarea rows="4" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="I'm interested in this property..."></textarea>
                            </div>
                            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors">
                                Send Message
                            </button>
                        </form>
                    </div>
                    
                    <div class="bg-gray-50 px-8 py-4">
                        <button @click="showContactModal = false" class="w-full text-gray-600 hover:text-gray-700 font-medium">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
        @livewireScriptConfig
    </body>
</html>
