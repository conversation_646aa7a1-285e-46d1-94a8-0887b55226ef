<x-layouts.public title="Find Your Perfect Home">
    <div class="min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="{{ route('home') }}" class="flex items-center space-x-2">
                            <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                Lokus
                            </h1>
                        </a>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-4">
                    <a href="{{ route('properties.index') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">Properties</a>
                    @auth
                        @if (auth()->user()->role === 'lister')
                            <a href="{{ route('properties.my') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">My Properties</a>
                            <a href="{{ route('properties.create') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">Create Listing</a>
                        @endif
                        @if (auth()->user()->role === 'admin')
                            <a href="{{ route('admin.dashboard') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">Admin</a>
                        @endif
                        <a href="{{ route('dashboard') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">Dashboard</a>
                        <form method="POST" action="{{ route('logout') }}" class="inline">
                            @csrf
                            <button type="submit" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
                                Logout
                            </button>
                        </form>
                    @else
                        <a href="{{ route('login') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">Sign In</a>
                        <a href="{{ route('register') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">Register</a>
                    @endauth
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="gradient-bg text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    Find Your Perfect
                    <span class="block">Dream Home</span>
                </h1>
                <p class="text-xl md:text-2xl mb-12 text-blue-100">
                    Discover amazing properties with our modern, intuitive platform
                </p>

                <!-- Search Bar -->
                <div class="max-w-4xl mx-auto bg-white p-6 rounded-2xl shadow-2xl">
                    <form action="{{ route('properties.index') }}" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="md:col-span-2">
                            <input
                                type="text"
                                name="keywords"
                                placeholder="Search by location, property type..."
                                value="{{ request('keywords') }}"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                            >
                        </div>
                        <select name="property_type" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900">
                            <option value="">All Types</option>
                            <option value="apartment" {{ request('property_type') === 'apartment' ? 'selected' : '' }}>Apartment</option>
                            <option value="house" {{ request('property_type') === 'house' ? 'selected' : '' }}>House</option>
                            <option value="land" {{ request('property_type') === 'land' ? 'selected' : '' }}>Land</option>
                            <option value="single-room" {{ request('property_type') === 'single-room' ? 'selected' : '' }}>Single Room</option>
                        </select>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            <i class="fas fa-search mr-2"></i>
                            Search
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Properties -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Properties</h2>
                <p class="text-xl text-gray-600">Discover our handpicked selection of premium properties</p>
            </div>

            @php
                $featuredProperties = \App\Models\Property::where('status', 'published')
                    ->with('user')
                    ->latest()
                    ->take(6)
                    ->get();
            @endphp

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @forelse($featuredProperties as $property)
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden card-hover">
                        <div class="relative">
                            @if($property->images && count($property->images) > 0)
                                <img src="/storage/{{ $property->images[0] }}" alt="{{ $property->title }}" class="w-full h-64 object-cover">
                            @else
                                <div class="w-full h-64 bg-gray-200 flex items-center justify-center">
                                    <i class="fas fa-home text-4xl text-gray-400"></i>
                                </div>
                            @endif
                            <div class="absolute top-4 left-4">
                                <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">{{ $property->listing_type }}</span>
                            </div>
                            <div class="absolute top-4 right-4">
                                <button class="bg-white/80 hover:bg-white text-gray-700 p-2 rounded-full transition-colors">
                                    <i class="fas fa-heart"></i>
                                </button>
                            </div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $property->title }}</h3>
                            <p class="text-gray-600 mb-3">{{ $property->city }}, {{ $property->state_region }}</p>
                            <div class="flex items-center justify-between mb-4">
                                <div class="text-2xl font-bold text-blue-600">
                                    ${{ number_format($property->price) }}
                                    @if($property->listing_type === 'For Rent')
                                        <span class="text-sm text-gray-500">/month</span>
                                    @endif
                                </div>
                            </div>
                            @if($property->features)
                                <div class="flex items-center text-sm text-gray-600 space-x-4">
                                    @if(isset($property->features['bedrooms']) && $property->features['bedrooms'] > 0)
                                        <div class="flex items-center">
                                            <i class="fas fa-bed mr-1"></i>
                                            <span>{{ $property->features['bedrooms'] }}</span>
                                        </div>
                                    @endif
                                    @if(isset($property->features['bathrooms']))
                                        <div class="flex items-center">
                                            <i class="fas fa-bath mr-1"></i>
                                            <span>{{ $property->features['bathrooms'] }}</span>
                                        </div>
                                    @endif
                                    @if(isset($property->features['square_footage']))
                                        <div class="flex items-center">
                                            <i class="fas fa-ruler-combined mr-1"></i>
                                            <span>{{ $property->features['square_footage'] }}</span> sqft
                                        </div>
                                    @endif
                                </div>
                            @endif
                            <div class="mt-4">
                                <a href="{{ route('properties.show', $property) }}" class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-span-full text-center py-12">
                        <i class="fas fa-home text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">No Properties Available</h3>
                        <p class="text-gray-500">Check back soon for new listings!</p>
                    </div>
                @endforelse
            </div>

            <div class="text-center mt-10">
                <a href="{{ route('properties.index') }}" class="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors">
                    View All Properties
                    <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Ready to List Your Property?</h2>
            <p class="text-xl text-gray-600 mb-8">Join thousands of property owners and agents using Lokus</p>
            @auth
                @if(auth()->user()->role === 'lister')
                    <a href="{{ route('properties.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-medium transition-colors inline-block">
                        Start Listing Today
                    </a>
                @else
                    <a href="{{ route('register') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-medium transition-colors inline-block">
                        Join as a Lister
                    </a>
                @endif
            @else
                <a href="{{ route('register') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-medium transition-colors inline-block">
                    Start Listing Today
                </a>
            @endauth
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <h3 class="text-2xl font-bold mb-4">Lokus</h3>
                    <p class="text-gray-300 mb-4">Your trusted platform for finding and listing properties. Connect with property seekers and listers in your area.</p>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="{{ route('properties.index') }}" class="text-gray-300 hover:text-white transition-colors">Browse Properties</a></li>
                        <li><a href="{{ route('register') }}" class="text-gray-300 hover:text-white transition-colors">List Property</a></li>
                        <li><a href="{{ route('login') }}" class="text-gray-300 hover:text-white transition-colors">Sign In</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Contact</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><i class="fas fa-envelope mr-2"></i><EMAIL></li>
                        <li><i class="fas fa-phone mr-2"></i>+****************</li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-300">
                <p>&copy; {{ date('Y') }} Lokus. All rights reserved.</p>
            </div>
        </div>
    </footer>

    </div>
</x-layouts.public>
